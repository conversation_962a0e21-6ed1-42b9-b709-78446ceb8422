import 'package:flame/game.dart';
import 'package:flutter/material.dart';
import 'package:run_chicken_run/components/splash_screen.dart';
import 'package:run_chicken_run/overlays/finish_false.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

import 'overlays/finish.dart';
import 'overlays/score.dart';
import 'overlays/start.dart';
import 'widgets/banner_ad_widget.dart';
import 'widgets/ad_removal_widget.dart';
import 'run_chicken_run.dart';

// Global widgets to image controller
final WidgetsToImageController widgetsToImageController =
    WidgetsToImageController();

class SplashApp extends StatefulWidget {
  const SplashApp({super.key});

  @override
  State<SplashApp> createState() => _SplashAppState();
}

class _SplashAppState extends State<SplashApp> {
  bool _showSplash = true;

  void _onSplashComplete() {
    setState(() {
      _showSplash = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_showSplash) {
      return MaterialApp(
        title: 'Run Chicken Run',
        debugShowCheckedModeBanner: false,
        home: SplashScreen(onSplashComplete: _onSplashComplete),
      );
    }

    return MaterialApp(
      title: 'Run Chicken Run',
      debugShowCheckedModeBanner: false,
      home: WidgetsToImage(
        controller: widgetsToImageController,
        child: Scaffold(
          body: Column(
            children: [
              // Banner ad at the top
              const BannerAdWidget(),
              // Game takes the remaining space
              Expanded(
                child: GameWidget<RunChickenRun>.controlled(
                  gameFactory: RunChickenRun.new,
                  overlayBuilderMap: {
                    'Start': (_, game) => Start(game: game),
                    'Finish': (_, game) => Finish(game: game),
                    'Finish_False': (_, game) => FinishFalse(game: game),
                    'Score': (_, game) => Score(game: game),
                  },
                  initialActiveOverlays: const ['Start'],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
