import 'package:flutter/material.dart';
import '../run_chicken_run.dart';
import '../components/score_manager.dart';
import 'package:animated_button/animated_button.dart';

class Start extends StatefulWidget {
  final RunChickenRun game;

  const Start({super.key, required this.game});

  @override
  State<Start> createState() => _StartState();
}

class _StartState extends State<Start> with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  // static AudioPlayer readySetGoAudio = AudioPlayer();
  // late final AudioCache audioCache;
  // static bool playPressed = false;

  // void playSound() async {
  //   readySetGoAudio = await FlameAudio.audioCache.play('sfx/rsg.mp3');
  // }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: 1.0,
      duration: const Duration(milliseconds: 300),
      child: Stack(
        children: [
          Container(
            padding: const EdgeInsets.all(55),
            child: Align(
              alignment: Alignment.bottomCenter,
              child: AnimatedBuilder(
                animation: _scaleAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: AnimatedButton(
                      onPressed: () async {
                        // Play button press sound
                        RunChickenRun.playSound('pressing_button.wav');

                        // Re-enable audience reactions for new game
                        RunChickenRun.audienceReactionsEnabled = true;

                        // Start ready-set-go sequence (ready sound will play when visual appears)
                        RunChickenRun.startReadySetGoSequence();

                        // Trigger scale animation
                        _scaleController.forward().then((_) {
                          _scaleController.reverse();
                        });
                        // Temporary: Reset ALL data for testing from beginning
                        await RunChickenRun.resetFirstTime();
                        await ScoreManager.resetAllData();

                        // Reload the score manager to hide displays
                        await widget.game.scoreManager.reloadData();

                        // Stop background music when game starts
                        RunChickenRun.stopBackgroundMusic();

                        widget.game.overlays.remove('Start');
                        widget.game.remove(RunChickenRun.chickenWalk);
                        widget.game.remove(RunChickenRun.audiences1);
                        widget.game.remove(RunChickenRun.ground1);
                        widget.game.remove(RunChickenRun.gameTitle);
                        widget.game.add(RunChickenRun.audiences2);
                        widget.game.add(RunChickenRun.ground2);
                        widget.game.add(RunChickenRun.marks);
                        widget.game.add(RunChickenRun.oneHundred);
                        widget.game.add(RunChickenRun.runTimerDisplay);
                        widget.game.add(RunChickenRun.chickenReady);
                        RunChickenRun.canRun = true;
                        if (RunChickenRun.isPeriodsAdded == false) {
                          widget.game.add(RunChickenRun.periods);
                        }
                        await Future.delayed(const Duration(milliseconds: 584));
                        if (RunChickenRun.isPeriodsAdded &&
                            RunChickenRun.canRun) {
                          widget.game.remove(RunChickenRun.periods);
                        }

                        if (RunChickenRun.isReadyAdded == false &&
                            RunChickenRun.canRun) {
                          widget.game.add(RunChickenRun.readySign);
                          // Play Ready sound when visual appears (synchronized)
                          RunChickenRun.playReadySound();
                          RunChickenRun.runTimerStartSignal.timer.start();
                        }
                        await Future.delayed(const Duration(milliseconds: 583));
                        if (RunChickenRun.isReadyAdded &&
                            RunChickenRun.canRun) {
                          widget.game.remove(RunChickenRun.readySign);
                        }
                        if (RunChickenRun.isSetAdded == false &&
                            RunChickenRun.canRun) {
                          widget.game.add(RunChickenRun.setSign);
                          // Play Set sound
                          RunChickenRun.playSetSound();
                        }
                        await Future.delayed(const Duration(milliseconds: 583));
                        if (RunChickenRun.isSetAdded && RunChickenRun.canRun) {
                          widget.game.remove(RunChickenRun.setSign);
                        }
                        if (RunChickenRun.isGoAdded == false &&
                            RunChickenRun.canRun) {
                          widget.game.add(RunChickenRun.goSign);
                          // Play Go sound
                          RunChickenRun.playGoSound();
                          RunChickenRun.canTap = true;

                          // Start persistent reminder timer for player engagement
                          RunChickenRun.persistentTapReminderTimer.timer
                              .start();

                          // Show TAP TAP instruction for first-time players
                          if (RunChickenRun.isFirstTime &&
                              !RunChickenRun.isTapInstructionAdded) {
                            widget.game.add(RunChickenRun.tapInstructionStroke);
                            widget.game.add(RunChickenRun.tapInstructionFill);
                            RunChickenRun.isTapInstructionAdded = true;
                            RunChickenRun.tapInstructionTimer.timer.start();
                            // Mark as not first time after showing instruction
                            RunChickenRun.markNotFirstTime();
                          }
                        }
                      },
                      width: 100,
                      height: 65,
                      color: Colors.amber,
                      duration: 50,
                      child: const Icon(
                        // Icons.play_arrow_rounded,
                        Icons.play_arrow_rounded,
                        size: 56.0,
                        color: Colors.deepOrange,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
