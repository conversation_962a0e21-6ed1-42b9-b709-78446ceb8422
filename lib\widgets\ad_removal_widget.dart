import 'package:flutter/material.dart';
import 'package:animated_button/animated_button.dart';
import '../services/iap_manager.dart';
import '../services/ad_manager.dart';

class AdRemovalWidget extends StatefulWidget {
  const AdRemovalWidget({super.key});

  @override
  State<AdRemovalWidget> createState() => _AdRemovalWidgetState();
}

class _AdRemovalWidgetState extends State<AdRemovalWidget> {
  bool _isPurchasing = false;

  @override
  Widget build(BuildContext context) {
    final iapManager = IAPManager.instance;
    final adManager = AdManager.instance;

    // Don't show if ads are already removed
    if (adManager.isAdRemovalPurchased) {
      return Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.green.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green.shade300, width: 2),
        ),
        child: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green.shade700, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Ads Removed! Thank you for supporting indie development! 🎉',
                style: TextStyle(
                  color: Colors.green.shade800,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      );
    }

    final product = iapManager.adRemovalProduct;
    if (product == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.amber.shade100, Colors.orange.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.shade300, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.star, color: Colors.orange.shade700, size: 24),
              const SizedBox(width: 8),
              Text(
                'Go Premium!',
                style: TextStyle(
                  color: Colors.orange.shade800,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '• Remove all ads forever\n• Support indie game development\n• Enjoy uninterrupted gameplay',
            style: TextStyle(
              color: Colors.orange.shade700,
              fontSize: 14,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 12),
          Center(
            child: AnimatedButton(
              onPressed: _isPurchasing ? () {} : _purchaseAdRemoval,
              width: 200,
              height: 50,
              color: _isPurchasing ? Colors.grey : Colors.orange,
              duration: 50,
              child: _isPurchasing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      'Remove Ads ${product.price}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _purchaseAdRemoval() async {
    if (_isPurchasing) return;

    setState(() {
      _isPurchasing = true;
    });

    try {
      final success = await IAPManager.instance.purchaseAdRemoval();
      if (success) {
        // Purchase initiated successfully
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Purchase initiated... Please complete the payment.'),
            backgroundColor: Colors.blue,
          ),
        );
      } else {
        // Purchase failed
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Purchase failed. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isPurchasing = false;
        });
      }
    }
  }
}
