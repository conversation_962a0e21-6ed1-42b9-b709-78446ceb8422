import 'dart:io';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AdManager {
  static AdManager? _instance;
  static AdManager get instance => _instance ??= AdManager._();
  AdManager._();

  // Ad unit IDs - Replace with your actual AdMob IDs
  static String get _bannerAdUnitId => Platform.isAndroid
      ? 'ca-app-pub-3940256099942544/6300978111' // Test ID
      : 'ca-app-pub-3940256099942544/2934735716'; // Test ID

  static String get _interstitialAdUnitId => Platform.isAndroid
      ? 'ca-app-pub-3940256099942544/1033173712' // Test ID
      : 'ca-app-pub-3940256099942544/4411468910'; // Test ID

  static String get _rewardedAdUnitId => Platform.isAndroid
      ? 'ca-app-pub-3940256099942544/5224354917' // Test ID
      : 'ca-app-pub-3940256099942544/1712485313'; // Test ID

  // Ad instances
  BannerAd? _bannerAd;
  InterstitialAd? _interstitialAd;
  RewardedAd? _rewardedAd;

  // Ad state
  bool _isAdRemovalPurchased = false;
  bool _isBannerAdLoaded = false;
  bool _isInterstitialAdLoaded = false;
  bool _isRewardedAdLoaded = false;

  // Ad frequency control
  int _gameSessionCount = 0;
  DateTime? _lastInterstitialShown;

  // Initialize ads
  Future<void> initialize() async {
    await MobileAds.instance.initialize();
    await _loadAdRemovalStatus();

    if (!_isAdRemovalPurchased) {
      _loadBannerAd();
      _loadInterstitialAd();
      _loadRewardedAd();
    }
  }

  // Load ad removal status from preferences
  Future<void> _loadAdRemovalStatus() async {
    final prefs = await SharedPreferences.getInstance();
    _isAdRemovalPurchased = prefs.getBool('ad_removal_purchased') ?? false;
  }

  // Save ad removal status
  Future<void> setAdRemovalPurchased(bool purchased) async {
    _isAdRemovalPurchased = purchased;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('ad_removal_purchased', purchased);

    if (purchased) {
      // Remove all ads
      _bannerAd?.dispose();
      _bannerAd = null;
      _isBannerAdLoaded = false;
    }
  }

  // Getters
  bool get isAdRemovalPurchased => _isAdRemovalPurchased;
  bool get isBannerAdLoaded => _isBannerAdLoaded && !_isAdRemovalPurchased;
  BannerAd? get bannerAd => _isAdRemovalPurchased ? null : _bannerAd;

  // Load banner ad
  void _loadBannerAd() {
    if (_isAdRemovalPurchased) return;

    _bannerAd = BannerAd(
      adUnitId: _bannerAdUnitId,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          _isBannerAdLoaded = true;
        },
        onAdFailedToLoad: (ad, error) {
          _isBannerAdLoaded = false;
          ad.dispose();
          // Retry after 30 seconds
          Future.delayed(const Duration(seconds: 30), () {
            _loadBannerAd();
          });
        },
      ),
    );
    _bannerAd!.load();
  }

  // Load interstitial ad
  void _loadInterstitialAd() {
    if (_isAdRemovalPurchased) return;

    InterstitialAd.load(
      adUnitId: _interstitialAdUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          _isInterstitialAdLoaded = true;
        },
        onAdFailedToLoad: (error) {
          _isInterstitialAdLoaded = false;
          // Retry after 30 seconds
          Future.delayed(const Duration(seconds: 30), () {
            _loadInterstitialAd();
          });
        },
      ),
    );
  }

  // Load rewarded ad
  void _loadRewardedAd() {
    if (_isAdRemovalPurchased) return;

    RewardedAd.load(
      adUnitId: _rewardedAdUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          _rewardedAd = ad;
          _isRewardedAdLoaded = true;
        },
        onAdFailedToLoad: (error) {
          _isRewardedAdLoaded = false;
          // Retry after 30 seconds
          Future.delayed(const Duration(seconds: 30), () {
            _loadRewardedAd();
          });
        },
      ),
    );
  }

  // Show interstitial ad with frequency control
  void showInterstitialAd() {
    if (_isAdRemovalPurchased || !_isInterstitialAdLoaded) return;

    _gameSessionCount++;

    // Show interstitial every 3 game sessions, with 2-minute cooldown
    final now = DateTime.now();
    if (_gameSessionCount >= 3 &&
        (_lastInterstitialShown == null ||
            now.difference(_lastInterstitialShown!).inMinutes >= 2)) {
      _interstitialAd?.show();
      _lastInterstitialShown = now;
      _gameSessionCount = 0;
      _isInterstitialAdLoaded = false;

      // Load next interstitial
      _loadInterstitialAd();
    }
  }

  // Show rewarded ad for extra attempt
  void showRewardedAd({required Function() onRewarded}) {
    if (_isAdRemovalPurchased || !_isRewardedAdLoaded) return;

    _rewardedAd?.show(
      onUserEarnedReward: (ad, reward) {
        onRewarded();
      },
    );

    _isRewardedAdLoaded = false;
    // Load next rewarded ad
    _loadRewardedAd();
  }

  // Check if rewarded ad is available
  bool get isRewardedAdAvailable =>
      _isRewardedAdLoaded && !_isAdRemovalPurchased;

  // Dispose ads
  void dispose() {
    _bannerAd?.dispose();
    _interstitialAd?.dispose();
    _rewardedAd?.dispose();
  }
}
