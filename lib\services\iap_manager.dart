import 'dart:async';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'ad_manager.dart';

class IAPManager {
  static IAPManager? _instance;
  static IAPManager get instance => _instance ??= IAPManager._();
  IAPManager._();

  // Product IDs - Same across all app stores
  static const String adRemovalProductId = 'remove_ads';

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;

  bool _isAvailable = false;
  List<ProductDetails> _products = [];
  bool _purchasePending = false;

  // Initialize IAP
  Future<void> initialize() async {
    _isAvailable = await _inAppPurchase.isAvailable();

    if (_isAvailable) {
      await _loadProducts();
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdate,
        onDone: () => _subscription.cancel(),
        onError: (error) {
          // Handle error silently
        },
      );

      // Restore previous purchases
      await _restorePurchases();
    }
  }

  // Load products from store
  Future<void> _loadProducts() async {
    const Set<String> productIds = {adRemovalProductId};
    final ProductDetailsResponse response =
        await _inAppPurchase.queryProductDetails(productIds);

    if (response.notFoundIDs.isNotEmpty) {
      // Handle missing products silently
    }

    _products = response.productDetails;
  }

  // Handle purchase updates
  void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        _purchasePending = true;
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          _handleError(purchaseDetails.error!);
        } else if (purchaseDetails.status == PurchaseStatus.purchased ||
            purchaseDetails.status == PurchaseStatus.restored) {
          _handleSuccessfulPurchase(purchaseDetails);
        }

        if (purchaseDetails.pendingCompletePurchase) {
          _inAppPurchase.completePurchase(purchaseDetails);
        }

        _purchasePending = false;
      }
    }
  }

  // Handle successful purchase
  void _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) {
    if (purchaseDetails.productID == adRemovalProductId) {
      // Remove ads permanently
      AdManager.instance.setAdRemovalPurchased(true);
    }
  }

  // Handle purchase error
  void _handleError(IAPError error) {
    _purchasePending = false;
  }

  // Purchase ad removal
  Future<bool> purchaseAdRemoval() async {
    if (!_isAvailable || _purchasePending) return false;

    final ProductDetails? productDetails = _products
        .where((product) => product.id == adRemovalProductId)
        .firstOrNull;

    if (productDetails == null) return false;

    final PurchaseParam purchaseParam =
        PurchaseParam(productDetails: productDetails);

    try {
      _purchasePending = true;
      final bool success =
          await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      return success;
    } catch (e) {
      _purchasePending = false;
      return false;
    }
  }

  // Restore purchases
  Future<void> _restorePurchases() async {
    try {
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      // Handle restore error silently
    }
  }

  // Get ad removal product details
  ProductDetails? get adRemovalProduct {
    try {
      return _products
          .firstWhere((product) => product.id == adRemovalProductId);
    } catch (e) {
      return null;
    }
  }

  // Getters
  bool get isAvailable => _isAvailable;
  bool get isPurchasePending => _purchasePending;
  List<ProductDetails> get products => _products;

  // Dispose
  void dispose() {
    _subscription.cancel();
  }
}

// Extension for firstOrNull (if not available in your Dart version)
extension IterableExtension<T> on Iterable<T> {
  T? get firstOrNull {
    final iterator = this.iterator;
    if (iterator.moveNext()) {
      return iterator.current;
    }
    return null;
  }
}
