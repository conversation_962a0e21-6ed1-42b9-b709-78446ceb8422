import 'package:flame/components.dart';
import 'package:flame/effects.dart';
import 'package:flutter/material.dart';
import 'package:run_chicken_run/run_chicken_run.dart';
import '../services/ad_manager.dart';

class ScoreBoard extends SpriteComponent with HasGameRef<RunChickenRun> {
  ScoreBoard()
      : super(
          size: Vector2(350.0, 250.0),
          anchor: Anchor.center,
        );

  var velocity = Vector2(0, -1).normalized() * 1000;

  static List<TextComponent> scoreB4DataElements = List<TextComponent>.filled(
      2, TextComponent(size: Vector2(10, 10)),
      growable: false);

  static List<TextComponent> scoreBestDataElements = List<TextComponent>.filled(
      2, TextComponent(size: Vector2(10, 10)),
      growable: false);

  static List<TextComponent> gradeB4DataElements = List<TextComponent>.filled(
      2, TextComponent(size: Vector2(10, 10)),
      growable: false);

  static List<TextComponent> gradeNowDataElements = List<TextComponent>.filled(
      2, TextComponent(size: Vector2(10, 10)),
      growable: false);

  static List<TextComponent> gradeBestDataElements = List<TextComponent>.filled(
      2, TextComponent(size: Vector2(10, 10)),
      growable: false);

  // bool isLastBestAdded = false;

  // bool isCurrentBestAdded = false;

  // bool isgradeNowAdded = false;

  // bool isgradeBestAdded = false;

  String gradeB4 = '';

  String gradeNow = '';

  String gradeBest = '';

  @override
  Future<void> onLoad() async {
    super.onLoad();
    sprite = await gameRef.loadSprite('scoreboard.png');
    opacity = 0.0;
  }

  createScoreB4Data() {
    scoreB4DataElements = [
      TextComponent(
        text: RunChickenRun.scoreB4.toStringAsPrecision(3),
        anchor: Anchor.center,
        position: Vector2(
          (size.x / 2) + 85,
          (size.y / 2) - 22,
        ),
        priority: 3,
        // size: Vector2.all(100),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: 36,
            fontFamily: '04B_19__',
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = 7
              ..color = Colors.black87,
          ),
        ),
      ),
      TextComponent(
        text: RunChickenRun.scoreB4.toStringAsPrecision(3),
        anchor: Anchor.center,
        position: Vector2(
          (size.x / 2) + 85,
          (size.y / 2) - 22,
        ),
        priority: 3,
        // size: Vector2.all(100),
        textRenderer: TextPaint(
          style: const TextStyle(
            fontSize: 36,
            color: Colors.white,
            fontFamily: '04B_19__',
          ),
        ),
      ),
    ];
    addAll(scoreB4DataElements);
  }

  createScoreBestData() {
    scoreBestDataElements = [
      TextComponent(
        text: RunChickenRun.scoreBest.toStringAsPrecision(3),
        anchor: Anchor.center,
        position: Vector2(
          (size.x / 2) + 85,
          (size.y / 2) - 22,
        ),
        priority: 3,
        // size: Vector2.all(100),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: 36,
            fontFamily: '04B_19__',
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = 7
              ..color = Colors.black87,
          ),
        ),
      ),
      TextComponent(
        text: RunChickenRun.scoreBest.toStringAsPrecision(3),
        anchor: Anchor.center,
        position: Vector2(
          (size.x / 2) + 85,
          (size.y / 2) - 22,
        ),
        priority: 3,
        // size: Vector2.all(100),
        textRenderer: TextPaint(
          style: const TextStyle(
            fontSize: 36,
            color: Colors.white,
            fontFamily: '04B_19__',
          ),
        ),
      ),
    ];
    addAll(scoreBestDataElements);
    RunChickenRun.isBestDataElementsAdded = true;
  }

  createGradeB4Data() {
    gradeNowDataElements = [
      TextComponent(
        text: gradeB4,
        anchor: Anchor.center,
        position: Vector2(
          (size.x / 2) + 85,
          (size.y / 2) + 32,
        ),
        priority: 3,
        // size: Vector2.all(100),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: 50,
            fontFamily: '04B_19__',
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = 7
              ..color = Colors.black87,
          ),
        ),
      ),
      TextComponent(
        text: gradeB4,
        anchor: Anchor.center,
        position: Vector2(
          (size.x / 2) + 85,
          (size.y / 2) + 32,
        ),
        priority: 3,
        // size: Vector2.all(100),
        textRenderer: TextPaint(
          style: const TextStyle(
            fontSize: 50,
            color: Colors.white,
            fontFamily: '04B_19__',
          ),
        ),
      ),
    ];
    addAll(gradeNowDataElements);
    RunChickenRun.isGradeNowDataElementsAdded = true;
  }

  createGradeNowData() {
    gradeNowDataElements = [
      TextComponent(
        text: gradeNow,
        anchor: Anchor.center,
        position: Vector2(
          (size.x / 2) - 85,
          (size.y / 2) + 32,
        ),
        priority: 3,
        // size: Vector2.all(100),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: 50,
            fontFamily: '04B_19__',
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = 7
              ..color = Colors.black87,
          ),
        ),
      ),
      TextComponent(
        text: gradeNow,
        anchor: Anchor.center,
        position: Vector2(
          (size.x / 2) - 85,
          (size.y / 2) + 32,
        ),
        priority: 3,
        // size: Vector2.all(100),
        textRenderer: TextPaint(
          style: const TextStyle(
            fontSize: 50,
            color: Colors.white,
            fontFamily: '04B_19__',
          ),
        ),
      ),
    ];
    addAll(gradeNowDataElements);
    RunChickenRun.isGradeNowDataElementsAdded = true;
  }

  createGradeBestData() {
    gradeBestDataElements = [
      TextComponent(
        text: gradeBest,
        anchor: Anchor.center,
        position: Vector2(
          (size.x / 2) + 85,
          (size.y / 2) + 32,
        ),
        priority: 3,
        // size: Vector2.all(100),
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: 50,
            fontFamily: '04B_19__',
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = 7
              ..color = Colors.black87,
          ),
        ),
      ),
      TextComponent(
        text: gradeBest,
        anchor: Anchor.center,
        position: Vector2(
          (size.x / 2) + 85,
          (size.y / 2) + 32,
        ),
        priority: 3,
        // size: Vector2.all(100),
        textRenderer: TextPaint(
          style: const TextStyle(
            fontSize: 50,
            color: Colors.white,
            fontFamily: '04B_19__',
          ),
        ),
      ),
    ];
    addAll(gradeBestDataElements);
    RunChickenRun.isGradeBestDataElementsAdded = true;
  }

  @override
  void onMount() {
    RunChickenRun.isScoreBoardAdded = true;
    super.onMount();
  }

  @override
  Future<void> update(double dt) async {
    if (position.y >= (game.canvasSize.y / 2) - 65) {
      position += velocity * dt;
    } else if (position.y < (game.canvasSize.y / 2) - 65) {
      position.y = (game.canvasSize.y / 2) - 65;
      velocity = Vector2(0, -1).normalized() * 0;
      if (RunChickenRun.isBestDataElementsAdded) {
        removeAll(scoreBestDataElements);
        RunChickenRun.isBestDataElementsAdded = false;
      }
      if (RunChickenRun.isGradeNowDataElementsAdded) {
        removeAll(gradeNowDataElements);
        RunChickenRun.isGradeNowDataElementsAdded = false;
      }
      if (RunChickenRun.isGradeBestDataElementsAdded) {
        removeAll(gradeBestDataElements);
        RunChickenRun.isGradeBestDataElementsAdded = false;
      }
      createScoreB4Data();
      createGradeB4Data();
      game.overlays.add('Score');

      // Start rapid counting time sound during score animation
      RunChickenRun.startRapidCountingTime();

      await Future.delayed(const Duration(milliseconds: 1000));

      // Stop rapid counting time sound when animation finishes
      RunChickenRun.stopRapidCountingTime();

      removeAll(scoreB4DataElements);

      // Use the same logic as the main game for consistency
      if (RunChickenRun.isNewRecord) {
        RunChickenRun.scoreBest = RunChickenRun.scoreNow;
        // First: Trigger screen flash immediately
        Future.delayed(const Duration(milliseconds: 200), () {
          game.flashScreen();
          // Play camera shutter sound with the flash
          RunChickenRun.playCameraShutter();
          // Then: Add new record animation and stars after flash
          Future.delayed(const Duration(milliseconds: 400), () {
            game.add(RunChickenRun.newRecordAnimation);
            game.add(RunChickenRun.starAnimationOne);
            game.add(RunChickenRun.starAnimationTwo);
          });
        });
      } else {
        RunChickenRun.scoreBest = RunChickenRun.scoreB4;
      }

      createScoreBestData();

      if (RunChickenRun.scoreNow >= 8.00) {
        gradeNow = 'F';
      } else if (RunChickenRun.scoreNow >= 7.60) {
        gradeNow = 'E';
      } else if (RunChickenRun.scoreNow >= 7.20) {
        gradeNow = 'D';
      } else if (RunChickenRun.scoreNow >= 6.80) {
        gradeNow = 'C';
      } else if (RunChickenRun.scoreNow >= 6.40) {
        gradeNow = 'B';
      } else if (RunChickenRun.scoreNow >= 6.00) {
        gradeNow = 'A';
      } else {
        gradeNow = 'S';
      }

      if (RunChickenRun.scoreBest >= 8.00) {
        gradeBest = 'F';
      } else if (RunChickenRun.scoreBest >= 7.60) {
        gradeBest = 'E';
      } else if (RunChickenRun.scoreBest >= 7.20) {
        gradeBest = 'D';
      } else if (RunChickenRun.scoreBest >= 6.80) {
        gradeBest = 'C';
      } else if (RunChickenRun.scoreBest >= 6.40) {
        gradeBest = 'B';
      } else if (RunChickenRun.scoreBest >= 6.00) {
        gradeBest = 'A';
      } else {
        gradeBest = 'S';
      }

      createGradeNowData();

      // Add enhanced bounce animation to grade reveal
      Future.delayed(const Duration(milliseconds: 100), () {
        for (var component in gradeNowDataElements) {
          // First bounce - bigger and more dramatic
          component.add(ScaleEffect.to(
            Vector2.all(1.4),
            EffectController(duration: 0.15, curve: Curves.easeOut),
          ));
          Future.delayed(const Duration(milliseconds: 150), () {
            // Second bounce - smaller
            component.add(ScaleEffect.to(
              Vector2.all(0.9),
              EffectController(duration: 0.1, curve: Curves.easeIn),
            ));
            Future.delayed(const Duration(milliseconds: 100), () {
              // Final settle - slightly bigger than normal for emphasis
              component.add(ScaleEffect.to(
                Vector2.all(1.05),
                EffectController(duration: 0.15, curve: Curves.elasticOut),
              ));
              Future.delayed(const Duration(milliseconds: 150), () {
                // Return to normal size
                component.add(ScaleEffect.to(
                  Vector2.all(1.0),
                  EffectController(duration: 0.1, curve: Curves.easeOut),
                ));
              });
            });
          });
        }
      });

      createGradeBestData();

      RunChickenRun.scoreB4 = RunChickenRun.scoreBest;

      gradeB4 = gradeBest;

      game.overlays.add('Finish');

      // Show interstitial ad with frequency control
      AdManager.instance.showInterstitialAd();

      // Play audience reactions after score counting finishes
      Future.delayed(const Duration(milliseconds: 200), () {
        if (RunChickenRun.scoreNow >= 8.00) {
          // Time out - play booing first, then background music
          RunChickenRun.playBooingWithCallback(() {
            // Play background music 50ms after booing finishes
            Future.delayed(const Duration(milliseconds: 50), () {
              RunChickenRun.playBackgroundMusic();
            });
          });
        } else if (RunChickenRun.isNewRecord) {
          // New record - play applause after camera shutter, then background music
          Future.delayed(const Duration(milliseconds: 600), () {
            RunChickenRun.playApplauseWithCallback(() {
              // Play background music 50ms after applause finishes
              Future.delayed(const Duration(milliseconds: 50), () {
                RunChickenRun.playBackgroundMusic();
              });
            });
          });
        } else {
          // No new record - play disappointed first, then background music
          RunChickenRun.playDisappointedWithCallback(() {
            // Play background music 50ms after disappointed finishes
            Future.delayed(const Duration(milliseconds: 50), () {
              RunChickenRun.playBackgroundMusic();
            });
          });
        }
      });
    }

    if (opacity < 1) {
      opacity += 0.2;
    }
  }

  @override
  void onRemove() {
    velocity = Vector2(0, -1).normalized() * 1000;
    position = Vector2((game.canvasSize.x / 2), ((game.canvasSize.y / 2) - 25));
    RunChickenRun.isScoreBoardAdded = false;
    super.onRemove();
  }
}
