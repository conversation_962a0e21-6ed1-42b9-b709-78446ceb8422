import 'package:flame/flame.dart';
import 'package:flutter/material.dart';
import 'package:run_chicken_run/splash_app.dart';
import 'services/ad_manager.dart';
import 'services/iap_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize ad and IAP systems
  await AdManager.instance.initialize();
  await IAPManager.instance.initialize();

  // Optimize image rendering for pixel art
  Flame.images.clearCache();

  // Set device orientation and fullscreen
  await Flame.device.fullScreen();
  await Flame.device.setPortrait();

  runApp(const SplashApp());
}
