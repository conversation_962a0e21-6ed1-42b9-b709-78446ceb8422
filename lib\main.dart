import 'package:flame/flame.dart';
import 'package:flutter/material.dart';
import 'package:run_chicken_run/splash_app.dart';
import 'services/ad_manager.dart';
import 'services/iap_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize ad and IAP systems with error handling
  try {
    await AdManager.instance.initialize();
  } catch (e) {
    // Continue without ads if initialization fails
  }

  try {
    await IAPManager.instance.initialize();
  } catch (e) {
    // Continue without IAP if initialization fails
  }

  // Optimize image rendering for pixel art
  Flame.images.clearCache();

  // Set device orientation and fullscreen
  await Flame.device.fullScreen();
  await Flame.device.setPortrait();

  runApp(const SplashApp());
}
