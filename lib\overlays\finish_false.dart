import 'package:flame/components.dart';
import 'package:flutter/material.dart';
import '../run_chicken_run.dart';
import 'package:animated_button/animated_button.dart';

class FinishFalse extends StatelessWidget {
  final RunChickenRun game;

  const FinishFalse({super.key, required this.game});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(55),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Align(
            alignment: Alignment.bottomCenter,
            child: AnimatedButton(
              onPressed: () async {
                // Play button press sound
                RunChickenRun.playSound('pressing_button.wav');

                // Stop all sounds including audience reactions
                RunChickenRun.stopAllSounds();

                // Stop background music when restarting game
                RunChickenRun.stopBackgroundMusic();

                RunChickenRun.canRun = true;
                RunChickenRun.chickenReady.position =
                    Vector2(game.canvasSize.x / 2, game.canvasSize.y - 234);
                game.remove(RunChickenRun.falseStart);
                game.overlays.remove('Finish_False');
                game.add(RunChickenRun.oneHundred);
                game.add(RunChickenRun.runTimerDisplay);

                if (RunChickenRun.isPeriodsAdded == false) {
                  game.add(RunChickenRun.periods);
                }
                await Future.delayed(const Duration(milliseconds: 584));
                if (RunChickenRun.isPeriodsAdded && RunChickenRun.canRun) {
                  game.remove(RunChickenRun.periods);
                }
                if (RunChickenRun.isReadyAdded == false &&
                    RunChickenRun.canRun) {
                  // Re-enable audience reactions for new game
                  RunChickenRun.audienceReactionsEnabled = true;

                  // Start the ready-set-go sequence and play Ready sound immediately
                  RunChickenRun.startReadySetGoSequence();
                  RunChickenRun.playReadySound();
                  game.add(RunChickenRun.readySign);
                  RunChickenRun.runTimerStartSignal.timer.start();
                }
                await Future.delayed(const Duration(milliseconds: 583));
                if (RunChickenRun.isReadyAdded && RunChickenRun.canRun) {
                  game.remove(RunChickenRun.readySign);
                }
                if (RunChickenRun.isSetAdded == false && RunChickenRun.canRun) {
                  game.add(RunChickenRun.setSign);
                  // Play Set sound
                  RunChickenRun.playSetSound();
                }
                await Future.delayed(const Duration(milliseconds: 583));
                if (RunChickenRun.isSetAdded && RunChickenRun.canRun) {
                  game.remove(RunChickenRun.setSign);
                }
                if (RunChickenRun.isGoAdded == false && RunChickenRun.canRun) {
                  game.add(RunChickenRun.goSign);
                  // Play Go sound
                  RunChickenRun.playGoSound();
                  RunChickenRun.canTap = true;

                  // Start persistent reminder timer for player engagement on restart
                  RunChickenRun.persistentTapReminderTimer.timer.start();
                }
              },
              width: 100,
              height: 65,
              color: Colors.amber,
              duration: 50,
              child: const Icon(
                Icons.replay_circle_filled_rounded,
                size: 43.0,
                color: Colors.deepOrange,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
