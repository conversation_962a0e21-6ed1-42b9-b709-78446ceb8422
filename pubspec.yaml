name: run_chicken_run
description: Run Chicken Run Flame Game Project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=2.19.0 <3.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flame: ^1.5.0
  shared_preferences: ^2.2.2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  animated_button: ^0.2.0
  countup: ^0.1.4
  vibration: ^1.7.5
  flame_audio: ^2.11.0
  audioplayers: ^6.0.0
  widgets_to_image: ^1.0.0
  share_plus: ^7.2.1
  # Ad monetization
  google_mobile_ads: ^5.1.0
  # In-app purchases for ad removal
  in_app_purchase: ^3.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_native_splash: ^2.2.17
  flutter_launcher_icons: ^0.13.1

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

flutter_native_splash:
  color: "#ffffff"
  image: assets/images/glanced.png

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/chicken_icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/chicken_icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/images/chicken_icon.png"
    icon_size: 48 # min:48, max:256, default: 48

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/audio/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    # - family: Press_Start
    #   fonts:
    #     - asset: assets/fonts/Press_Start.ttf
    # - family: Monoton
    #   fonts:
    #     - asset: assets/fonts/Monoton_Regular.ttf
    - family: Digital
      fonts:
        - asset: assets/fonts/digital_dream.ttf
    # - family: Blocky
    #   fonts:
    #     - asset: assets/fonts/blocky.ttf
    - family: 04B_19__
      fonts:
        - asset: assets/fonts/04B_19__.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
